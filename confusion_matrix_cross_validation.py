# Confusion Matrix ile Cross-Validation
from sklearn.metrics import confusion_matrix, classification_report
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
from tqdm import tqdm

def evaluate_with_confusion_matrix(model, test_loader, device, class_names):
    """
    Model değerlendirmesi ve confusion matrix oluşturma
    """
    model.eval()
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for images, labels in test_loader:
            images, labels = images.to(device), labels.to(device)
            outputs = model(images)
            _, predicted = torch.max(outputs, 1)

            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    # Confusion Matrix hesapla
    cm = confusion_matrix(all_labels, all_preds)
    cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]

    # Accuracy hesapla
    accuracy = np.sum(np.array(all_preds) == np.array(all_labels)) / len(all_labels)

    return cm, cm_normalized, accuracy

def train_and_evaluate_with_cm(
    model, train_loader, test_loader, device, class_names,
    n_epochs=10, early_stopping_patience=3
):
    """
    Training fonksiyonu + Confusion Matrix
    """
    model.to(device)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters())

    best_acc = 0
    patience_counter = 0
    best_model_state = None

    for epoch in range(n_epochs):
        model.train()
        running_loss = 0.0

        print(f"\n🔁 Epoch {epoch+1}/{n_epochs} başlıyor...")

        for images, labels in tqdm(train_loader, desc=f"🧠 Eğitim {epoch+1}"):
            images, labels = images.to(device), labels.to(device)
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            running_loss += loss.item()

        # Değerlendirme
        model.eval()
        correct = 0
        total = 0
        with torch.no_grad():
            for images, labels in test_loader:
                images, labels = images.to(device), labels.to(device)
                outputs = model(images)
                _, predicted = torch.max(outputs, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()

        acc = correct / total
        print(f"📊 Epoch {epoch+1} - Loss: {running_loss:.4f} - Test Acc: {acc:.4f}")

        # En iyi modeli kaydet
        if acc > best_acc:
            best_acc = acc
            patience_counter = 0
            best_model_state = model.state_dict().copy()
        else:
            patience_counter += 1
            if patience_counter >= early_stopping_patience:
                print("🛑 Early stopping triggered.")
                break

    # En iyi modeli yükle
    if best_model_state is not None:
        model.load_state_dict(best_model_state)

    print(f"\n✅ En yüksek test doğruluğu: {best_acc:.4f}")

    # Confusion Matrix oluştur
    print("🎯 Confusion Matrix oluşturuluyor...")
    cm, cm_normalized, final_accuracy = evaluate_with_confusion_matrix(
        model, test_loader, device, class_names
    )

    return best_acc, cm, cm_normalized

def cross_validate_with_confusion_matrix(
    model_func, dataset, folds, device, class_names,
    model_name="Model", n_epochs=20, n_splits=5
):
    """
    Cross-validation + Confusion Matrix döndüren fonksiyon
    """
    fold_accuracies = []
    confusion_matrices = []
    
    print(f"\n🚀 {model_name} için {n_splits}-fold Cross-Validation başlıyor...")
    
    for fold_idx, (train_idx, val_idx) in enumerate(folds):
        print(f"\n📁 Fold {fold_idx + 1}/{n_splits} başlıyor...")
        
        # Dataset'leri ayır
        train_dataset = Subset(dataset, train_idx)
        val_dataset = Subset(dataset, val_idx)
        
        train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
        
        # Model oluştur
        model = model_func()
        model.to(device)
        
        # Training
        best_acc, cm, cm_normalized = train_and_evaluate_with_cm(
            model, train_loader, val_loader, device, class_names, n_epochs
        )
        
        fold_accuracies.append(best_acc)
        confusion_matrices.append((cm, cm_normalized))
        
        print(f"✅ Fold {fold_idx + 1} tamamlandı - Accuracy: {best_acc:.4f}")
    
    # Sonuçları göster
    mean_acc = np.mean(fold_accuracies)
    std_acc = np.std(fold_accuracies)
    
    print(f"\n🏆 {model_name} Sonuçları:")
    print(f"📊 5-fold sonuçları: {[f'{acc:.2%}' for acc in fold_accuracies]}")
    print(f"📈 Ortalama: {mean_acc:.2%} (±{std_acc:.2%})")
    
    # En iyi fold'u bul
    best_fold_idx = np.argmax(fold_accuracies)
    best_accuracy = fold_accuracies[best_fold_idx]
    best_cm, best_cm_normalized = confusion_matrices[best_fold_idx]
    
    print(f"🏆 En iyi fold: Fold {best_fold_idx + 1}")
    print(f"🎯 En iyi accuracy: {best_accuracy:.4f} ({best_accuracy:.2%})")
    
    return fold_accuracies, confusion_matrices, best_fold_idx

def plot_confusion_matrix(cm_normalized, class_names, fold_accuracies, model_name="Model"):
    """
    Confusion Matrix görselleştirme
    """
    plt.figure(figsize=(12, 10))
    
    # Heatmap oluştur
    sns.heatmap(cm_normalized, annot=True, fmt='.1%', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names,
                cbar_kws={'label': 'Percent (%)'})
    
    # Başlık ve etiketler
    mean_acc = np.mean(fold_accuracies)
    std_acc = np.std(fold_accuracies)
    fold_results = [f'{acc:.2%}' for acc in fold_accuracies]
    
    plt.title(f'{model_name} — Normalized Confusion Matrix (Percent)\n'
              f'Overall Accuracy: {mean_acc:.2%} (±{std_acc:.2%}) • '
              f'5-fold: {fold_results}', fontsize=14)
    
    plt.ylabel('True Class')
    plt.xlabel('Predicted Class')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.show()

# KULLANIM ÖRNEĞİ:
"""
# Class names tanımla
class_names = [
    'Actinic Keratosis', 'Basal Cell Carcinoma', 'Dermatofibroma',
    'Melanoma', 'Nevus', 'Pigmented Benign Keratosis',
    'Seborrheic Keratosis', 'Squamous Cell Carcinoma', 'Vascular Lesion'
]

# Yeni fonksiyonu çalıştır
fold_accuracies, confusion_matrices, best_fold_idx = cross_validate_with_confusion_matrix(
    get_efficientnet_model,
    dataset,
    folds,
    device,
    class_names,
    model_name="EfficientNet-B0",
    n_epochs=20
)

# En iyi fold'un confusion matrix'ini göster
best_cm, best_cm_normalized = confusion_matrices[best_fold_idx]
plot_confusion_matrix(best_cm_normalized, class_names, fold_accuracies, "EfficientNet-B0")
"""
