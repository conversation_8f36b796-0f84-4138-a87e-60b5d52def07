{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1OU5DHIrkn3e", "outputId": "05f53ea0-f745-4e7b-b2fc-fdd18a2dcc96"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Dataset URL: https://www.kaggle.com/datasets/pritpal2873/multiple-skin-disease-detection-and-classification\n", "License(s): apache-2.0\n", "Downloading multiple-skin-disease-detection-and-classification.zip to /content\n", " 97% 762M/785M [00:07<00:00, 216MB/s]\n", "100% 785M/785M [00:07<00:00, 108MB/s]\n"]}], "source": ["!pip install -q kaggle\n", "!mkdir -p ~/.kaggle\n", "!cp /content/kaggle.json ~/.kaggle/kaggle.json\n", "!chmod 600 ~/.kaggle/kaggle.json\n", "\n", "!kaggle datasets download -d pritpal2873/multiple-skin-disease-detection-and-classification\n", "\n", "!unzip -q \"multiple-skin-disease-detection-and-classification.zip\" -d \"skin_data\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sTgE2t5jmBFy", "outputId": "f8cfe057-4dea-4977-b6bb-060deb2ca632"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["💻 Kullanılan cihaz: cuda\n", "Sınıflar: ['Acitinic Keratosis', 'Basal Cell Carcinoma', 'Dermatofibroma', 'Melanoma', 'Nevus', 'Pigmented Benign Keratosis', 'Seborrheic Keratosis', 'Squamous Cell Carcinoma', 'Vascular Lesion']\n", "Toplam Görüntü: 4107\n"]}], "source": ["import torch\n", "from torchvision import datasets, transforms\n", "from torch.utils.data import DataLoader\n", "from sklearn.model_selection import StratifiedKFold\n", "import numpy as np\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"💻 Kullanılan cihaz: {device}\")\n", "\n", "transform = transforms.Compose([\n", "    transforms.Resize((224, 224)),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "])\n", "\n", "dataset = datasets.ImageFolder(\"skin_data/Skin Cancer Dataset\", transform=transform)\n", "\n", "print(\"Sınıflar:\", dataset.classes)\n", "print(\"Toplam Görüntü:\", len(dataset))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tYjG7LY4mJO-", "outputId": "ea07c136-0ea3-430b-e359-97a65333387f"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["1. Fold - Train örnek sayısı: 3285\n", "1. Fold - Test örnek sayısı: 822\n"]}], "source": ["from sklearn.model_selection import StratifiedKFold\n", "from torch.utils.data import Subset\n", "\n", "# Etiketleri numpy array olarak al\n", "targets = np.array(dataset.targets)\n", "\n", "# 5 fold <PERSON><PERSON><PERSON>\n", "skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "\n", "# Foldlara göre index listesi oluştur\n", "folds = []\n", "for train_idx, test_idx in skf.split(np.zeros(len(targets)), targets):\n", "    folds.append((train_idx, test_idx))\n", "\n", "# 1. fold i<PERSON><PERSON> ö<PERSON> veri loader’ları oluştur\n", "train_dataset = Subset(dataset, folds[0][0])\n", "test_dataset = Subset(dataset, folds[0][1])\n", "\n", "train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)\n", "test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)\n", "\n", "# Kontrol\n", "print(\"1. Fold - Train örnek sayısı:\", len(train_dataset))\n", "print(\"1. Fold - Test örnek sayısı:\", len(test_dataset))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ufaBjLEZmOFz"}, "outputs": [], "source": ["import torch.nn as nn\n", "import torch.nn.functional as F\n", "\n", "class CompactCNN(nn.Module):\n", "    def __init__(self, num_classes=9):\n", "        super(Compact<PERSON><PERSON><PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(3, 16, 3, padding=1)\n", "        self.conv2 = nn.Conv2d(16, 32, 3, padding=1)\n", "        self.pool = nn.MaxPool2d(2, 2)\n", "        self.fc1 = nn.<PERSON>ar(32 * 56 * 56, 128)\n", "        self.fc2 = nn.Linear(128, num_classes)\n", "\n", "    def forward(self, x):\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv1(x)))  # 224 -> 112\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv2(x)))  # 112 -> 56\n", "        x = x.view(-1, 32 * 56 * 56)\n", "        x = F.relu(self.fc1(x))\n", "        x = self.fc2(x)\n", "        return x\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_M7tDynPmUqz"}, "outputs": [], "source": ["class AdvancedCNN(nn.Module):\n", "    def __init__(self, num_classes=9):\n", "        super(AdvancedCN<PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(3, 32, 3, padding=1)\n", "        self.conv2 = nn.Conv2d(32, 64, 3, padding=1)\n", "        self.conv3 = nn.Conv2d(64, 128, 3, padding=1)\n", "        self.pool = nn.MaxPool2d(2, 2)\n", "        self.dropout = nn.Dropout(0.3)\n", "        self.fc1 = nn.Linear(128 * 28 * 28, 256)\n", "        self.fc2 = nn.Linear(256, num_classes)\n", "\n", "    def forward(self, x):\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv1(x)))  # 224 -> 112\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv2(x)))  # 112 -> 56\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv3(x)))  # 56 -> 28\n", "        x = x.view(-1, 128 * 28 * 28)\n", "        x = self.dropout(x)\n", "        x = F.relu(self.fc1(x))\n", "        x = self.fc2(x)\n", "        return x\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "b3CMAVP1maq4"}, "outputs": [], "source": ["import torch.optim as optim\n", "from tqdm import tqdm\n", "\n", "def train_and_evaluate(\n", "    model, train_loader, test_loader, device,\n", "    n_epochs=10, early_stopping_patience=3\n", "):\n", "    model.to(device)\n", "    criterion = nn.CrossEntropyLoss()\n", "    optimizer = optim.<PERSON>(model.parameters())\n", "\n", "    best_acc = 0\n", "    patience_counter = 0\n", "\n", "    for epoch in range(n_epochs):\n", "        model.train()\n", "        running_loss = 0.0\n", "\n", "        print(f\"\\n🔁 Epoch {epoch+1}/{n_epochs} başlıyor...\")\n", "\n", "        for images, labels in tqdm(train_loader, desc=f\"🧠 Eğitim {epoch+1}\"):\n", "            images, labels = images.to(device), labels.to(device)\n", "            optimizer.zero_grad()\n", "            outputs = model(images)\n", "            loss = criterion(outputs, labels)\n", "            loss.backward()\n", "            optimizer.step()\n", "            running_loss += loss.item()\n", "\n", "        # Değerlendirme\n", "        model.eval()\n", "        correct = 0\n", "        total = 0\n", "        with torch.no_grad():\n", "            for images, labels in test_loader:\n", "                images, labels = images.to(device), labels.to(device)\n", "                outputs = model(images)\n", "                _, predicted = torch.max(outputs, 1)\n", "                total += labels.size(0)\n", "                correct += (predicted == labels).sum().item()\n", "\n", "        acc = correct / total\n", "        print(f\"📊 Epoch {epoch+1} - Loss: {running_loss:.4f} - Test Acc: {acc:.4f}\")\n", "\n", "        # Early stopping\n", "        if acc > best_acc:\n", "            best_acc = acc\n", "            patience_counter = 0\n", "        else:\n", "            patience_counter += 1\n", "            if patience_counter >= early_stopping_patience:\n", "                print(\"🛑 Early stopping triggered.\")\n", "                break\n", "\n", "    print(f\"\\n✅ En yüksek test doğruluğu: {best_acc:.4f}\")\n", "    return best_acc\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fGShbNgrUp76"}, "outputs": [], "source": ["def cross_validate_model(model_fn, dataset, folds, device, model_name=\"Model\", n_epochs=70):\n", "    acc_list = []\n", "\n", "    print(f\"\\n🚀 Başlıyor: {model_name} için 5-Fold Cross-Validation\\n{'-'*60}\")\n", "\n", "    for fold_idx, (train_idx, test_idx) in enumerate(folds):\n", "        print(f\"\\n📂 Fold {fold_idx + 1}/5\")\n", "\n", "        train_dataset = Subset(dataset, train_idx)\n", "        test_dataset = Subset(dataset, test_idx)\n", "\n", "        train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)\n", "        test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)\n", "\n", "        model = model_fn(num_classes=9)\n", "\n", "        acc = train_and_evaluate(\n", "            model, train_loader, test_loader, device,\n", "            n_epochs=n_epochs,\n", "            early_stopping_patience=5\n", "        )\n", "        acc_list.append(acc)\n", "        print(f\"✅ Fold {fold_idx + 1} Accuracy: {acc:.4f}\")\n", "\n", "    print(f\"\\n🏁 {model_name} i<PERSON><PERSON> Accuracy: {np.mean(acc_list):.4f} (std: {np.std(acc_list):.4f})\")\n", "    return acc_list\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "47h95H_fmdUM", "outputId": "47f7ddd5-4763-4f20-b2da-8bb27df65509"}, "outputs": [{"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["\n", "🚀 Başlıyor: CompactCNN için 5-Fold Cross-Validation\n", "------------------------------------------------------------\n", "\n", "📂 Fold 1/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [01:54<00:00,  1.12s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 1 - Loss: 212.2325 - Test Acc: 0.3114\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:45<00:00,  1.03s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 2 - Loss: 184.3501 - Test Acc: 0.3479\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [01:42<00:00,  1.00it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 3 - Loss: 162.8666 - Test Acc: 0.4063\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [01:42<00:00,  1.01it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 4 - Loss: 150.1532 - Test Acc: 0.4416\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [01:42<00:00,  1.00it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 5 - Loss: 136.6327 - Test Acc: 0.4477\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:42<00:00,  1.01it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 6 - Loss: 123.5933 - Test Acc: 0.4696\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 7 - Loss: 112.0277 - Test Acc: 0.4574\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [01:41<00:00,  1.02it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 8 - Loss: 97.4220 - Test Acc: 0.4903\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 9 - Loss: 80.9118 - Test Acc: 0.5061\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [01:40<00:00,  1.02it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 10 - Loss: 67.0841 - Test Acc: 0.4805\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 11 - Loss: 51.2347 - Test Acc: 0.4745\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [01:41<00:00,  1.02it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 12 - Loss: 40.3672 - Test Acc: 0.4915\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 13 - Loss: 29.7596 - Test Acc: 0.4878\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [01:40<00:00,  1.02it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 14 - Loss: 24.7071 - Test Acc: 0.4745\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.5061\n", "✅ Fold 1 Accuracy: 0.5061\n", "\n", "📂 Fold 2/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [01:41<00:00,  1.02it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 1 - Loss: 221.2846 - Test Acc: 0.3175\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:41<00:00,  1.02it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 2 - Loss: 179.0285 - Test Acc: 0.3929\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 3 - Loss: 161.1122 - Test Acc: 0.4161\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [01:41<00:00,  1.02it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 4 - Loss: 151.9279 - Test Acc: 0.4635\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [01:41<00:00,  1.02it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 5 - Loss: 138.2289 - Test Acc: 0.4355\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 6 - Loss: 129.7495 - Test Acc: 0.4732\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 7 - Loss: 121.0855 - Test Acc: 0.4562\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [01:40<00:00,  1.03it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 8 - Loss: 109.1897 - Test Acc: 0.4757\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [01:40<00:00,  1.02it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 9 - Loss: 95.4683 - Test Acc: 0.5219\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 10 - Loss: 84.2294 - Test Acc: 0.4647\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 11 - Loss: 72.3427 - Test Acc: 0.4623\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [01:41<00:00,  1.02it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 12 - Loss: 65.2130 - Test Acc: 0.4878\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [01:41<00:00,  1.02it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 13 - Loss: 58.5545 - Test Acc: 0.4562\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [01:41<00:00,  1.02it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 14 - Loss: 50.8405 - Test Acc: 0.4976\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.5219\n", "✅ Fold 2 Accuracy: 0.5219\n", "\n", "📂 Fold 3/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [01:45<00:00,  1.03s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 1 - Loss: 228.4901 - Test Acc: 0.2741\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:50<00:00,  1.08s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 2 - Loss: 189.7903 - Test Acc: 0.3276\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [01:51<00:00,  1.09s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 3 - Loss: 175.0151 - Test Acc: 0.3849\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [01:52<00:00,  1.09s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 4 - Loss: 163.4982 - Test Acc: 0.4068\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 5 - Loss: 152.5046 - Test Acc: 0.4555\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 6 - Loss: 140.0486 - Test Acc: 0.4336\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [01:51<00:00,  1.09s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 7 - Loss: 131.0255 - Test Acc: 0.4263\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [01:51<00:00,  1.08s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 8 - Loss: 120.2829 - Test Acc: 0.4421\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [01:48<00:00,  1.06s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 9 - Loss: 107.9189 - Test Acc: 0.4555\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 10 - Loss: 93.7997 - Test Acc: 0.4714\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [01:45<00:00,  1.03s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 11 - Loss: 78.8766 - Test Acc: 0.4860\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 12 - Loss: 69.2569 - Test Acc: 0.5030\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [01:46<00:00,  1.03s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 13 - Loss: 57.7010 - Test Acc: 0.4896\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [01:46<00:00,  1.03s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 14 - Loss: 51.0268 - Test Acc: 0.4982\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 15 - Loss: 38.7753 - Test Acc: 0.4872\n", "\n", "🔁 Epoch 16/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 16: 100%|██████████| 103/103 [01:48<00:00,  1.06s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 16 - Loss: 28.9506 - Test Acc: 0.4836\n", "\n", "🔁 Epoch 17/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 17: 100%|██████████| 103/103 [01:46<00:00,  1.03s/it]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 17 - Loss: 26.6760 - Test Acc: 0.4982\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.5030\n", "✅ Fold 3 Accuracy: 0.5030\n", "\n", "📂 Fold 4/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 1 - Loss: 211.3548 - Test Acc: 0.2911\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:42<00:00,  1.00it/s]\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["📊 Epoch 2 - Loss: 187.6917 - Test Acc: 0.4056\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 169.4685 - Test Acc: 0.4787\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [01:42<00:00,  1.00it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 160.1054 - Test Acc: 0.4178\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [01:41<00:00,  1.02it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 143.0688 - Test Acc: 0.4446\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 127.1295 - Test Acc: 0.5079\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 109.6890 - Test Acc: 0.4641\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [01:42<00:00,  1.01it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 94.7295 - Test Acc: 0.5030\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 79.3872 - Test Acc: 0.5225\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [01:42<00:00,  1.01it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 59.4996 - Test Acc: 0.5225\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [01:42<00:00,  1.01it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 48.1561 - Test Acc: 0.5091\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [01:40<00:00,  1.03it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 35.6391 - Test Acc: 0.5128\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [01:41<00:00,  1.01it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 26.0492 - Test Acc: 0.5055\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [01:42<00:00,  1.00it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 24.2874 - Test Acc: 0.5018\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.5225\n", "✅ Fold 4 Accuracy: 0.5225\n", "\n", "📂 Fold 5/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 220.1488 - Test Acc: 0.3167\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 183.9629 - Test Acc: 0.3812\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 165.6997 - Test Acc: 0.4287\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [01:43<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 151.8349 - Test Acc: 0.4275\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 142.1369 - Test Acc: 0.4324\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:43<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 133.4620 - Test Acc: 0.4373\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [01:43<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 124.7110 - Test Acc: 0.4568\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [01:43<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 111.8809 - Test Acc: 0.4312\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 102.3880 - Test Acc: 0.4738\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 92.1286 - Test Acc: 0.4909\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 81.1354 - Test Acc: 0.4653\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 76.4634 - Test Acc: 0.4616\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 63.4869 - Test Acc: 0.4836\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 55.0380 - Test Acc: 0.4616\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 15 - Loss: 48.8154 - Test Acc: 0.5079\n", "\n", "🔁 Epoch 16/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 16: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 16 - Loss: 41.1237 - Test Acc: 0.4836\n", "\n", "🔁 Epoch 17/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 17: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 17 - Loss: 28.8448 - Test Acc: 0.4799\n", "\n", "🔁 Epoch 18/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 18: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 18 - Loss: 27.4390 - Test Acc: 0.4616\n", "\n", "🔁 Epoch 19/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 19: 100%|██████████| 103/103 [01:43<00:00,  1.00s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 19 - Loss: 25.1071 - Test Acc: 0.4848\n", "\n", "🔁 Epoch 20/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 20: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 20 - Loss: 19.8079 - Test Acc: 0.4848\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.5079\n", "✅ Fold 5 Accuracy: 0.5079\n", "\n", "🏁 CompactCNN için Ortalama Accuracy: 0.5123 (std: 0.0083)\n"]}], "source": ["compactcnn_results = cross_validate_model(\n", "    CompactCNN,\n", "    dataset,\n", "    folds,\n", "    device,\n", "    model_name=\"CompactCNN\",\n", "    n_epochs=70\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pLGUiKd4zePt"}, "outputs": [], "source": ["import torch.nn as nn\n", "import torch.nn.functional as F\n", "\n", "class AdvancedCNN(nn.Module):\n", "    def __init__(self, num_classes=9):\n", "        super(AdvancedCN<PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(3, 32, 3, padding=1)\n", "        self.conv2 = nn.Conv2d(32, 64, 3, padding=1)\n", "        self.conv3 = nn.Conv2d(64, 128, 3, padding=1)\n", "        self.pool = nn.MaxPool2d(2, 2)\n", "        self.dropout = nn.Dropout(0.3)\n", "        self.fc1 = nn.Linear(128 * 28 * 28, 256)\n", "        self.fc2 = nn.Linear(256, num_classes)\n", "\n", "    def forward(self, x):\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv1(x)))  # 224 -> 112\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv2(x)))  # 112 -> 56\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv3(x)))  # 56 -> 28\n", "        x = x.view(-1, 128 * 28 * 28)\n", "        x = self.dropout(x)\n", "        x = F.relu(self.fc1(x))\n", "        x = self.fc2(x)\n", "        return x\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lMceDpstzjuk", "outputId": "5a5d010b-1066-4bc0-ea0b-fbd5cc14f0b5"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "🚀 Başlıyor: AdvancedCNN için 5-Fold Cross-Validation\n", "------------------------------------------------------------\n", "\n", "📂 Fold 1/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [02:01<00:00,  1.18s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 213.7971 - Test Acc: 0.2968\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:46<00:00,  1.04s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 187.2910 - Test Acc: 0.3248\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 173.4391 - Test Acc: 0.3771\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [01:46<00:00,  1.03s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 156.7640 - Test Acc: 0.4380\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [01:45<00:00,  1.03s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 148.0074 - Test Acc: 0.4477\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 137.5954 - Test Acc: 0.4562\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 125.1304 - Test Acc: 0.4526\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 115.9213 - Test Acc: 0.4647\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 103.7251 - Test Acc: 0.4440\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 89.6143 - Test Acc: 0.4611\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [01:46<00:00,  1.03s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 78.1937 - Test Acc: 0.4623\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 67.1157 - Test Acc: 0.4550\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 57.7531 - Test Acc: 0.4842\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 49.1875 - Test Acc: 0.4830\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [01:45<00:00,  1.03s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 15 - Loss: 35.4586 - Test Acc: 0.4842\n", "\n", "🔁 Epoch 16/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 16: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 16 - Loss: 32.1980 - Test Acc: 0.4878\n", "\n", "🔁 Epoch 17/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 17: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 17 - Loss: 25.9016 - Test Acc: 0.4951\n", "\n", "🔁 Epoch 18/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 18: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 18 - Loss: 22.8936 - Test Acc: 0.4842\n", "\n", "🔁 Epoch 19/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 19: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 19 - Loss: 25.0701 - Test Acc: 0.4903\n", "\n", "🔁 Epoch 20/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 20: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 20 - Loss: 17.2819 - Test Acc: 0.4781\n", "\n", "🔁 Epoch 21/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 21: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 21 - Loss: 18.0997 - Test Acc: 0.4866\n", "\n", "🔁 Epoch 22/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 22: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 22 - Loss: 15.8706 - Test Acc: 0.4903\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.4951\n", "✅ Fold 1 Accuracy: 0.4951\n", "\n", "📂 Fold 2/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 210.5785 - Test Acc: 0.2908\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 185.8125 - Test Acc: 0.3564\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 170.2103 - Test Acc: 0.3686\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 153.6033 - Test Acc: 0.4392\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 136.8602 - Test Acc: 0.4440\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 125.3647 - Test Acc: 0.4818\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 107.5293 - Test Acc: 0.4818\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 95.2563 - Test Acc: 0.4903\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 77.4624 - Test Acc: 0.4793\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 63.6716 - Test Acc: 0.4659\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 50.2263 - Test Acc: 0.4818\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 38.0100 - Test Acc: 0.4903\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [01:47<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 32.0066 - Test Acc: 0.4769\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.4903\n", "✅ Fold 2 Accuracy: 0.4903\n", "\n", "📂 Fold 3/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [01:49<00:00,  1.06s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 216.5296 - Test Acc: 0.2728\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:49<00:00,  1.06s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 187.0010 - Test Acc: 0.3666\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [01:49<00:00,  1.06s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 167.6623 - Test Acc: 0.4044\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 153.3738 - Test Acc: 0.4446\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 143.3355 - Test Acc: 0.4397\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:47<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 127.4192 - Test Acc: 0.4495\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [01:47<00:00,  1.04s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 116.6638 - Test Acc: 0.4848\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 101.7824 - Test Acc: 0.4555\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 91.8666 - Test Acc: 0.4933\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 79.7020 - Test Acc: 0.4677\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 65.7751 - Test Acc: 0.5201\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 52.2664 - Test Acc: 0.5006\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [01:49<00:00,  1.07s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 46.2515 - Test Acc: 0.4896\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 37.8818 - Test Acc: 0.5067\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 15 - Loss: 33.5087 - Test Acc: 0.4823\n", "\n", "🔁 Epoch 16/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 16: 100%|██████████| 103/103 [01:47<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 16 - Loss: 30.5361 - Test Acc: 0.5018\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.5201\n", "✅ Fold 3 Accuracy: 0.5201\n", "\n", "📂 Fold 4/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 215.4718 - Test Acc: 0.3301\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 183.9340 - Test Acc: 0.3959\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 166.1954 - Test Acc: 0.4592\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 150.2443 - Test Acc: 0.4872\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 142.5208 - Test Acc: 0.4811\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:43<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 131.8899 - Test Acc: 0.4665\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 121.3996 - Test Acc: 0.4982\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 104.5925 - Test Acc: 0.5030\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 91.4959 - Test Acc: 0.4970\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [01:43<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 79.6208 - Test Acc: 0.5274\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 69.2460 - Test Acc: 0.4994\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [01:45<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 58.3167 - Test Acc: 0.5164\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 46.6573 - Test Acc: 0.5055\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [01:44<00:00,  1.02s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 40.9193 - Test Acc: 0.4994\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [01:44<00:00,  1.01s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 15 - Loss: 33.4320 - Test Acc: 0.4957\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.5274\n", "✅ Fold 4 Accuracy: 0.5274\n", "\n", "📂 Fold 5/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [01:47<00:00,  1.04s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 215.8376 - Test Acc: 0.2765\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:48<00:00,  1.06s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 189.9330 - Test Acc: 0.3435\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [01:47<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 171.5656 - Test Acc: 0.4385\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [01:47<00:00,  1.04s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 155.6345 - Test Acc: 0.4153\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [01:46<00:00,  1.04s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 149.8001 - Test Acc: 0.4555\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:47<00:00,  1.04s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 134.4182 - Test Acc: 0.4361\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [01:47<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 123.4771 - Test Acc: 0.4312\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [01:47<00:00,  1.04s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 113.0973 - Test Acc: 0.4592\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 100.0867 - Test Acc: 0.4665\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 88.6642 - Test Acc: 0.4848\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [01:47<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 79.0421 - Test Acc: 0.4896\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [01:47<00:00,  1.04s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 66.8814 - Test Acc: 0.5067\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 57.9656 - Test Acc: 0.4957\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [01:48<00:00,  1.05s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 52.8556 - Test Acc: 0.4921\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [01:54<00:00,  1.11s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 15 - Loss: 40.6609 - Test Acc: 0.4787\n", "\n", "🔁 Epoch 16/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 16: 100%|██████████| 103/103 [01:50<00:00,  1.08s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 16 - Loss: 37.2013 - Test Acc: 0.4909\n", "\n", "🔁 Epoch 17/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 17: 100%|██████████| 103/103 [01:50<00:00,  1.07s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 17 - Loss: 33.8745 - Test Acc: 0.4811\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.5067\n", "✅ Fold 5 Accuracy: 0.5067\n", "\n", "🏁 AdvancedCNN için <PERSON> Accuracy: 0.5079 (std: 0.0142)\n"]}], "source": ["advancedcnn_results = cross_validate_model(\n", "    AdvancedCNN,\n", "    dataset,\n", "    folds,\n", "    device,\n", "    model_name=\"AdvancedCNN\",\n", "    n_epochs=70\n", ")"]}, {"cell_type": "code", "source": ["from torchvision.models import mobilenet_v3_large\n", "\n", "def get_mobilenetv3_model(num_classes=9):\n", "    model = mobilenet_v3_large(pretrained=True)\n", "\n", "    # Tü<PERSON> ağı fine-tune edecek şekilde aç\n", "    for param in model.parameters():\n", "        param.requires_grad = True\n", "\n", "    # Classifier'ı değiştir\n", "    model.classifier[3] = nn.Linear(model.classifier[3].in_features, num_classes)\n", "\n", "    return model\n"], "metadata": {"id": "za3NJbJ09yp1"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["mobilenetv3_results = cross_validate_model(\n", "    get_mobilenetv3_model,\n", "    dataset,\n", "    folds,\n", "    device,\n", "    model_name=\"MobileNetV3\",\n", "    n_epochs=70\n", ")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XLI4Y48drmjZ", "outputId": "5d047979-78d9-40e0-85a0-a585ab80efa8"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.12/dist-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.\n", "  warnings.warn(\n", "/usr/local/lib/python3.12/dist-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=MobileNet_V3_Large_Weights.IMAGENET1K_V1`. You can also use `weights=MobileNet_V3_Large_Weights.DEFAULT` to get the most up-to-date weights.\n", "  warnings.warn(msg)\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "🚀 Başlıyor: MobileNetV3 için 5-Fold Cross-Validation\n", "------------------------------------------------------------\n", "\n", "📂 Fold 1/5\n", "Downloading: \"https://download.pytorch.org/models/mobilenet_v3_large-8738ca79.pth\" to /root/.cache/torch/hub/checkpoints/mobilenet_v3_large-8738ca79.pth\n"]}, {"output_type": "stream", "name": "stderr", "text": ["100%|██████████| 21.1M/21.1M [00:00<00:00, 202MB/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [02:01<00:00,  1.18s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 129.7359 - Test Acc: 0.3260\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:59<00:00,  1.16s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 83.8490 - Test Acc: 0.3443\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [02:00<00:00,  1.17s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 58.4413 - Test Acc: 0.3771\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [02:01<00:00,  1.18s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 48.2049 - Test Acc: 0.5170\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [02:00<00:00,  1.17s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 40.0616 - Test Acc: 0.5779\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:59<00:00,  1.16s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 27.3411 - Test Acc: 0.6509\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [01:59<00:00,  1.16s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 20.3865 - Test Acc: 0.6302\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [02:00<00:00,  1.17s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 16.6968 - Test Acc: 0.6642\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [01:59<00:00,  1.16s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 19.4381 - Test Acc: 0.5535\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [01:59<00:00,  1.16s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 16.7430 - Test Acc: 0.6618\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [01:58<00:00,  1.15s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 14.7156 - Test Acc: 0.5912\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [01:58<00:00,  1.15s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 17.2053 - Test Acc: 0.6058\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [02:00<00:00,  1.17s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 13.7461 - Test Acc: 0.5669\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.6642\n", "✅ Fold 1 Accuracy: 0.6642\n", "\n", "📂 Fold 2/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [01:57<00:00,  1.15s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 129.6716 - Test Acc: 0.1569\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:58<00:00,  1.15s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 83.3463 - Test Acc: 0.2798\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [01:57<00:00,  1.14s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 63.0931 - Test Acc: 0.5328\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [01:58<00:00,  1.15s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 47.3284 - Test Acc: 0.4647\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [01:59<00:00,  1.16s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 34.4420 - Test Acc: 0.5572\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:57<00:00,  1.14s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 30.0600 - Test Acc: 0.4684\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [01:58<00:00,  1.15s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 25.8314 - Test Acc: 0.6156\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [01:58<00:00,  1.16s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 21.2194 - Test Acc: 0.6168\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [01:58<00:00,  1.15s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 20.8279 - Test Acc: 0.6813\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [02:00<00:00,  1.17s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 16.8442 - Test Acc: 0.6448\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [02:00<00:00,  1.17s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 9.2404 - Test Acc: 0.6594\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [01:59<00:00,  1.16s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 15.6698 - Test Acc: 0.5925\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [01:59<00:00,  1.16s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 18.6288 - Test Acc: 0.6448\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [02:00<00:00,  1.17s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 13.6566 - Test Acc: 0.6594\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.6813\n", "✅ Fold 2 Accuracy: 0.6813\n", "\n", "📂 Fold 3/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [02:02<00:00,  1.19s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 129.0424 - Test Acc: 0.2168\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [02:02<00:00,  1.19s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 84.0829 - Test Acc: 0.2899\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [02:02<00:00,  1.19s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 61.1514 - Test Acc: 0.3325\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [02:01<00:00,  1.18s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 51.4342 - Test Acc: 0.4884\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [02:04<00:00,  1.20s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 37.3061 - Test Acc: 0.4823\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [02:03<00:00,  1.20s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 27.7701 - Test Acc: 0.6638\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [02:05<00:00,  1.22s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 25.0338 - Test Acc: 0.5871\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [02:04<00:00,  1.21s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 18.9954 - Test Acc: 0.6212\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [02:05<00:00,  1.22s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 19.0499 - Test Acc: 0.5445\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [02:04<00:00,  1.21s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 17.6550 - Test Acc: 0.4616\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [02:03<00:00,  1.20s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 13.4090 - Test Acc: 0.6663\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [02:01<00:00,  1.18s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 12.7762 - Test Acc: 0.5067\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [02:02<00:00,  1.19s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 13.0351 - Test Acc: 0.5579\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [02:03<00:00,  1.20s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 13.7138 - Test Acc: 0.5238\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [02:03<00:00,  1.20s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 15 - Loss: 12.0026 - Test Acc: 0.5932\n", "\n", "🔁 Epoch 16/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 16: 100%|██████████| 103/103 [02:02<00:00,  1.19s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 16 - Loss: 10.5995 - Test Acc: 0.6516\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.6663\n", "✅ Fold 3 Accuracy: 0.6663\n", "\n", "📂 Fold 4/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [02:01<00:00,  1.18s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 131.5194 - Test Acc: 0.3520\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [01:58<00:00,  1.15s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 84.8576 - Test Acc: 0.3155\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [01:59<00:00,  1.16s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 64.4442 - Test Acc: 0.2753\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [01:58<00:00,  1.15s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 49.2971 - Test Acc: 0.5603\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [02:01<00:00,  1.18s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 38.2206 - Test Acc: 0.6066\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [01:59<00:00,  1.16s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 27.0227 - Test Acc: 0.6456\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [02:06<00:00,  1.23s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 25.5507 - Test Acc: 0.4458\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [02:07<00:00,  1.24s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 21.4078 - Test Acc: 0.6005\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [02:03<00:00,  1.20s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 20.0979 - Test Acc: 0.6322\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [02:00<00:00,  1.17s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 19.2268 - Test Acc: 0.6285\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [02:05<00:00,  1.22s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 14.5794 - Test Acc: 0.6918\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [02:07<00:00,  1.23s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 17.1222 - Test Acc: 0.6066\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [02:04<00:00,  1.21s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 13.8151 - Test Acc: 0.6675\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [02:00<00:00,  1.17s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 13.2578 - Test Acc: 0.6724\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [01:58<00:00,  1.15s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 15 - Loss: 14.1591 - Test Acc: 0.6577\n", "\n", "🔁 Epoch 16/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 16: 100%|██████████| 103/103 [02:02<00:00,  1.19s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 16 - Loss: 8.9710 - Test Acc: 0.6772\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.6918\n", "✅ Fold 4 Accuracy: 0.6918\n", "\n", "📂 Fold 5/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [02:04<00:00,  1.21s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 130.8038 - Test Acc: 0.1632\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [02:03<00:00,  1.20s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 85.9090 - Test Acc: 0.3934\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [02:04<00:00,  1.21s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 62.4501 - Test Acc: 0.4434\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [02:04<00:00,  1.21s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 45.6098 - Test Acc: 0.4080\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [02:03<00:00,  1.20s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 32.9688 - Test Acc: 0.6480\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [02:04<00:00,  1.21s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 28.7058 - Test Acc: 0.5993\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [02:05<00:00,  1.22s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 26.6501 - Test Acc: 0.5579\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [02:03<00:00,  1.20s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 19.6959 - Test Acc: 0.5834\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [02:04<00:00,  1.21s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 19.6142 - Test Acc: 0.6407\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [02:05<00:00,  1.22s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 20.8111 - Test Acc: 0.5834\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.6480\n", "✅ Fold 5 Accuracy: 0.6480\n", "\n", "🏁 MobileNetV3 i<PERSON>in <PERSON> Accuracy: 0.6703 (std: 0.0151)\n"]}]}, {"cell_type": "code", "source": ["from torchvision.models import efficientnet_b0\n", "\n", "def get_efficientnet_model(num_classes=9):\n", "    model = efficientnet_b0(pretrained=True)\n", "\n", "    # Tü<PERSON> ağı fine-tune edecek şekilde aç\n", "    for param in model.parameters():\n", "        param.requires_grad = True\n", "\n", "    # Classifier'ı değiştir\n", "    model.classifier[1] = nn.Linear(model.classifier[1].in_features, num_classes)\n", "\n", "    return model\n"], "metadata": {"id": "oWCihSwhn17k"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["efficientnet_results = cross_validate_model(\n", "    get_efficientnet_model,\n", "    dataset,\n", "    folds,\n", "    device,\n", "    model_name=\"EfficientNet-B0\",\n", "    n_epochs=70\n", ")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "k2GOZ8yMn3cp", "outputId": "ea88ea64-8a88-4a64-a6a0-b70fc702c805"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "🚀 Başlıyor: EfficientNet-B0 için 5-Fold Cross-Validation\n", "------------------------------------------------------------\n", "\n", "📂 Fold 1/5\n", "Downloading: \"https://download.pytorch.org/models/efficientnet_b0_rwightman-7f5810bc.pth\" to /root/.cache/torch/hub/checkpoints/efficientnet_b0_rwightman-7f5810bc.pth\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.12/dist-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=EfficientNet_B0_Weights.IMAGENET1K_V1`. You can also use `weights=EfficientNet_B0_Weights.DEFAULT` to get the most up-to-date weights.\n", "  warnings.warn(msg)\n", "100%|██████████| 20.5M/20.5M [00:00<00:00, 139MB/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [02:09<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 137.4497 - Test Acc: 0.6314\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [02:07<00:00,  1.24s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 85.1064 - Test Acc: 0.6995\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [02:08<00:00,  1.24s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 66.4275 - Test Acc: 0.7032\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [02:11<00:00,  1.27s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 50.2492 - Test Acc: 0.6983\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [02:09<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 36.8837 - Test Acc: 0.7141\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [02:09<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 27.9296 - Test Acc: 0.7129\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [02:09<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 25.7422 - Test Acc: 0.7275\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 18.9057 - Test Acc: 0.7238\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [02:10<00:00,  1.27s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 16.0867 - Test Acc: 0.6715\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [02:09<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 19.0012 - Test Acc: 0.7117\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 18.4628 - Test Acc: 0.7311\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 15.3283 - Test Acc: 0.7226\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 13.3456 - Test Acc: 0.7080\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [02:09<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 8.3441 - Test Acc: 0.7214\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [02:09<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 15 - Loss: 6.9990 - Test Acc: 0.7238\n", "\n", "🔁 Epoch 16/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 16: 100%|██████████| 103/103 [02:08<00:00,  1.24s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 16 - Loss: 7.7347 - Test Acc: 0.6776\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.7311\n", "✅ Fold 1 Accuracy: 0.7311\n", "\n", "📂 Fold 2/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [02:10<00:00,  1.27s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 134.9461 - Test Acc: 0.6107\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 90.3525 - Test Acc: 0.6752\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 64.5264 - Test Acc: 0.7202\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 46.2750 - Test Acc: 0.7141\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 34.4982 - Test Acc: 0.7129\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 26.0497 - Test Acc: 0.6606\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [02:10<00:00,  1.27s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 30.3567 - Test Acc: 0.7348\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [02:09<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 20.4345 - Test Acc: 0.7153\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [02:10<00:00,  1.27s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 17.1849 - Test Acc: 0.7141\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [02:09<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 17.9081 - Test Acc: 0.6484\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 17.1384 - Test Acc: 0.7384\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 10.9075 - Test Acc: 0.7457\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [02:08<00:00,  1.24s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 13.8228 - Test Acc: 0.7287\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 9.6149 - Test Acc: 0.7336\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [02:09<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 15 - Loss: 9.9731 - Test Acc: 0.7275\n", "\n", "🔁 Epoch 16/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 16: 100%|██████████| 103/103 [02:08<00:00,  1.24s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 16 - Loss: 6.9970 - Test Acc: 0.7056\n", "\n", "🔁 Epoch 17/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 17: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 17 - Loss: 9.8826 - Test Acc: 0.6898\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.7457\n", "✅ Fold 2 Accuracy: 0.7457\n", "\n", "📂 Fold 3/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [02:13<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 134.9325 - Test Acc: 0.6261\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [02:12<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 87.9280 - Test Acc: 0.6650\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [02:13<00:00,  1.30s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 66.2457 - Test Acc: 0.7186\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [02:12<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 46.9121 - Test Acc: 0.6748\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [02:13<00:00,  1.30s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 40.4771 - Test Acc: 0.6894\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [02:13<00:00,  1.30s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 30.5628 - Test Acc: 0.6541\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [02:13<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 27.2522 - Test Acc: 0.7150\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [02:12<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 18.2116 - Test Acc: 0.7345\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [02:13<00:00,  1.30s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 14.6873 - Test Acc: 0.7186\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [02:13<00:00,  1.30s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 14.6817 - Test Acc: 0.7174\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [02:13<00:00,  1.30s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 22.2862 - Test Acc: 0.7052\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [02:12<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 12.6339 - Test Acc: 0.7199\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [02:13<00:00,  1.30s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 10.3683 - Test Acc: 0.7357\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [02:14<00:00,  1.31s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 10.5877 - Test Acc: 0.7345\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [02:12<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 15 - Loss: 12.3233 - Test Acc: 0.7223\n", "\n", "🔁 Epoch 16/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 16: 100%|██████████| 103/103 [02:13<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 16 - Loss: 12.0452 - Test Acc: 0.7077\n", "\n", "🔁 Epoch 17/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 17: 100%|██████████| 103/103 [02:12<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 17 - Loss: 10.3948 - Test Acc: 0.7296\n", "\n", "🔁 Epoch 18/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 18: 100%|██████████| 103/103 [02:13<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 18 - Loss: 9.5509 - Test Acc: 0.7259\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.7357\n", "✅ Fold 3 Accuracy: 0.7357\n", "\n", "📂 Fold 4/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [02:09<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 139.2112 - Test Acc: 0.6529\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [02:09<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 88.6319 - Test Acc: 0.7162\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 62.1145 - Test Acc: 0.7028\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [02:09<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 50.3458 - Test Acc: 0.7454\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 35.8231 - Test Acc: 0.7174\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 28.8411 - Test Acc: 0.7223\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [02:09<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 26.4885 - Test Acc: 0.7491\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 20.2540 - Test Acc: 0.7150\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [02:08<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 21.7925 - Test Acc: 0.7515\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [02:07<00:00,  1.24s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 16.3285 - Test Acc: 0.7272\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [02:09<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 16.0629 - Test Acc: 0.7272\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [02:09<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 11.6718 - Test Acc: 0.7381\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [02:10<00:00,  1.27s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 11.0934 - Test Acc: 0.7540\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [02:09<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 11.1243 - Test Acc: 0.7284\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [02:11<00:00,  1.28s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 15 - Loss: 10.3337 - Test Acc: 0.7540\n", "\n", "🔁 Epoch 16/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 16: 100%|██████████| 103/103 [02:10<00:00,  1.26s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 16 - Loss: 11.3208 - Test Acc: 0.7333\n", "\n", "🔁 Epoch 17/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 17: 100%|██████████| 103/103 [02:10<00:00,  1.27s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 17 - Loss: 11.1037 - Test Acc: 0.7235\n", "\n", "🔁 Epoch 18/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 18: 100%|██████████| 103/103 [02:09<00:00,  1.25s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 18 - Loss: 9.0394 - Test Acc: 0.7454\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.7540\n", "✅ Fold 4 Accuracy: 0.7540\n", "\n", "📂 Fold 5/5\n", "\n", "🔁 Epoch 1/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 1: 100%|██████████| 103/103 [02:11<00:00,  1.28s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 1 - Loss: 133.5485 - Test Acc: 0.5871\n", "\n", "🔁 Epoch 2/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 2: 100%|██████████| 103/103 [02:11<00:00,  1.28s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 2 - Loss: 87.2171 - Test Acc: 0.6273\n", "\n", "🔁 Epoch 3/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 3: 100%|██████████| 103/103 [02:11<00:00,  1.27s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 3 - Loss: 64.4257 - Test Acc: 0.7052\n", "\n", "🔁 Epoch 4/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 4: 100%|██████████| 103/103 [02:14<00:00,  1.30s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 4 - Loss: 46.1198 - Test Acc: 0.6638\n", "\n", "🔁 Epoch 5/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 5: 100%|██████████| 103/103 [02:12<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 5 - Loss: 36.9462 - Test Acc: 0.7040\n", "\n", "🔁 Epoch 6/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 6: 100%|██████████| 103/103 [02:11<00:00,  1.28s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 6 - Loss: 26.6897 - Test Acc: 0.7065\n", "\n", "🔁 Epoch 7/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 7: 100%|██████████| 103/103 [02:11<00:00,  1.28s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 7 - Loss: 25.5728 - Test Acc: 0.6979\n", "\n", "🔁 Epoch 8/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 8: 100%|██████████| 103/103 [02:13<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 8 - Loss: 20.3897 - Test Acc: 0.6529\n", "\n", "🔁 Epoch 9/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 9: 100%|██████████| 103/103 [02:11<00:00,  1.28s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 9 - Loss: 19.9463 - Test Acc: 0.6991\n", "\n", "🔁 Epoch 10/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 10: 100%|██████████| 103/103 [02:12<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 10 - Loss: 12.6573 - Test Acc: 0.7296\n", "\n", "🔁 Epoch 11/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 11: 100%|██████████| 103/103 [02:11<00:00,  1.28s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 11 - Loss: 11.7362 - Test Acc: 0.7284\n", "\n", "🔁 Epoch 12/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 12: 100%|██████████| 103/103 [02:11<00:00,  1.28s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 12 - Loss: 14.8569 - Test Acc: 0.6784\n", "\n", "🔁 Epoch 13/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 13: 100%|██████████| 103/103 [02:11<00:00,  1.28s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 13 - Loss: 12.8587 - Test Acc: 0.6931\n", "\n", "🔁 Epoch 14/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 14: 100%|██████████| 103/103 [02:12<00:00,  1.29s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 14 - Loss: 11.6698 - Test Acc: 0.7296\n", "\n", "🔁 Epoch 15/70 başlıyor...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["🧠 Eğitim 15: 100%|██████████| 103/103 [02:11<00:00,  1.27s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["📊 Epoch 15 - Loss: 7.3930 - Test Acc: 0.7138\n", "🛑 Early stopping triggered.\n", "\n", "✅ En yüksek test doğruluğu: 0.7296\n", "✅ Fold 5 Accuracy: 0.7296\n", "\n", "🏁 EfficientNet-B0 i<PERSON>in <PERSON>ama Accuracy: 0.7392 (std: 0.0093)\n"]}]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "machine_shape": "hm", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}