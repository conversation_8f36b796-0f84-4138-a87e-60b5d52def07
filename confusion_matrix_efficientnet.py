# CONFUSION MATRIX İLE EFFİCİENTNET ÇALIŞTIR

def cross_validate_with_confusion_matrix(
    model_func, dataset, folds, device, class_names, 
    model_name="Model", n_epochs=20, early_stopping_patience=3
):
    """
    Cross-validation + Confusion Matrix
    """
    fold_results = []
    all_confusion_matrices = []
    
    print(f"\n🚀 {model_name} Cross-Validation Başlıyor...")
    print(f"📊 {len(folds)} fold, {n_epochs} epoch, early stopping: {early_stopping_patience}")
    
    for fold_idx, (train_idx, val_idx) in enumerate(folds):
        print(f"\n{'='*50}")
        print(f"🔄 FOLD {fold_idx + 1}/{len(folds)}")
        print(f"{'='*50}")
        
        # Dataset split
        train_dataset = Subset(dataset, train_idx)
        val_dataset = Subset(dataset, val_idx)
        
        train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True, num_workers=2)
        val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False, num_workers=2)
        
        # Model oluştur
        model = model_func()
        
        # Train + Confusion Matrix
        best_acc, cm, cm_normalized = train_and_evaluate_with_cm(
            model, train_loader, val_loader, device, class_names,
            n_epochs=n_epochs, early_stopping_patience=early_stopping_patience
        )
        
        fold_results.append(best_acc)
        all_confusion_matrices.append((cm, cm_normalized))
        
        print(f"✅ Fold {fold_idx + 1} tamamlandı - Accuracy: {best_acc:.4f}")
    
    # Sonuçları özetle
    mean_acc = np.mean(fold_results)
    std_acc = np.std(fold_results)
    
    print(f"\n🏆 {model_name} SONUÇLARI:")
    print(f"📈 Ortalama Accuracy: {mean_acc:.4f} (±{std_acc:.4f})")
    print(f"📊 Fold Sonuçları: {[f'{acc:.4f}' for acc in fold_results]}")
    
    # En iyi fold'u bul
    best_fold_idx = np.argmax(fold_results)
    best_acc = fold_results[best_fold_idx]
    
    print(f"🥇 En İyi Fold: {best_fold_idx + 1} - Accuracy: {best_acc:.4f}")
    
    # En iyi fold'un confusion matrix'ini göster
    best_cm, best_cm_normalized = all_confusion_matrices[best_fold_idx]
    
    plt.figure(figsize=(14, 12))
    sns.heatmap(best_cm_normalized, annot=True, fmt='.1%', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names,
                cbar_kws={'label': 'Percent (%)'})
    
    plt.title(f'{model_name} — Normalized Confusion Matrix (Percent)\n'
              f'Overall Accuracy: {mean_acc:.2%} (±{std_acc:.2%}) • '
              f'5-fold: {[f"{acc:.2%}" for acc in fold_results]}')
    plt.ylabel('True Class')
    plt.xlabel('Predicted Class')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.show()
    
    return fold_results, all_confusion_matrices

# CONFUSION MATRIX İLE ÇALIŞTIR
print("🔥 EfficientNet-B0 Confusion Matrix ile çalıştırılıyor...")

efficientnet_results, confusion_matrices = cross_validate_with_confusion_matrix(
    get_efficientnet_model,
    dataset,
    folds,
    device,
    class_names,
    model_name="EfficientNet-B0",
    n_epochs=20,
    early_stopping_patience=3
)
